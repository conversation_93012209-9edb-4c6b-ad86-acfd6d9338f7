/**
 * Database schema definitions for DermaCare bi-directional sync service
 *
 * This module defines the PostgreSQL database schema using Drizzle ORM for the
 * DermaCare data synchronization service. The schema supports bi-directional
 * data sync between CliniCore (CC) and AutoPatient (AP) platforms with proper
 * relationship management, data integrity, and performance optimization.
 *
 * **Schema Design Principles:**
 * - Maintains data from both platforms for comparison and conflict resolution
 * - Uses UUID primary keys for distributed system compatibility
 * - Stores platform-specific timestamps for sync coordination
 * - Implements JSONB columns for flexible data storage
 * - Includes proper indexing for query performance
 *
 * **Key Features:**
 * - Patient-appointment relationship management
 * - Custom field caching for both platforms
 * - Comprehensive error logging with deduplication
 * - Audit trail with creation and update timestamps
 * - Platform-specific data preservation
 *
 * **Performance Optimizations:**
 * - Unique indexes on platform-specific IDs
 * - JSONB columns for efficient JSON operations
 * - Proper foreign key relationships
 * - Optimized query patterns for sync operations
 *
 * @example
 * ```typescript
 * // Query patient with appointments
 * const patientWithAppointments = await db
 *   .select()
 *   .from(patient)
 *   .leftJoin(appointment, eq(appointment.patientId, patient.id))
 *   .where(eq(patient.ccId, 123));
 *
 * // Insert new patient record
 * const newPatient = await db.insert(patient).values({
 *   apId: "ap_contact_123",
 *   ccId: 456,
 *   email: "<EMAIL>",
 *   apData: apContactData,
 *   ccData: ccPatientData
 * });
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import type {
	APContactWebhookPayload,
	APWebhookPayload,
	APGetCustomFieldType,
	GetAPAppointmentType,
	GetAPContactType,
	GetCCAppointmentType,
	GetCCCustomfieldsType,
	GetCCPatientType,
} from "@type";
import { relations } from "drizzle-orm";
import {
	customType,
	integer,
	jsonb,
	pgTable,
	text,
	timestamp,
	varchar,
} from "drizzle-orm/pg-core";
import type { CCWebhookPayload } from "@/processors/ccWebhook";

/**
 * Custom JSONB column type with proper TypeScript typing
 *
 * Creates a strongly-typed JSONB column that automatically handles JSON
 * serialization/deserialization while maintaining type safety throughout
 * the application. This is essential for storing complex platform-specific
 * data structures.
 *
 * @template TData - TypeScript type for the JSON data structure
 * @param name - Column name in the database
 * @returns Drizzle column definition with proper typing
 *
 * @example
 * ```typescript
* // Define a JSONB column for AP contact data
 * const apData = customJsonb<GetAPContactType>("ap_data");
 *
 * // Usage in table definition
 * export const patient = pgTable("patients", {
 *   apData: customJsonb<GetAPContactType>("ap_data"),
 *   ccData: customJsonb<GetCCPatientType>("cc_data")
 * });
 *
```
*/
const customJsonb = <TData>(name: string) =>
	customType<{ data: TData; driverData: string }>({
		dataType() {
			return "jsonb";
		},
		toDriver(value: TData): string {
			return JSON.stringify(value);
		},
	})(name);

/**
 * Common columns shared across all tables
 *
 * Provides consistent structure for all database tables including:
 * - UUID primary key for distributed system compatibility
 * - Creation timestamp for audit trails
 * - Update timestamp with automatic updates
 *
 * **Column Details:**
 * - `id`: UUID primary key, automatically generated
 * - `createdAt`: Timestamp when record was created, defaults to now
 * - `updatedAt`: Timestamp when record was last updated, auto-updates
 *
 * @example
 *
```typescript
* // Use in table definition
 * export const myTable = pgTable("my_table", {
 *   ...commonColumns,
 *   customField: varchar("custom_field", { length: 255 })
 * });
 *
```
*/
const commonColumns = {
	/** UUID primary key, automatically generated using crypto.randomUUID() */
	id: varchar("id", { length: 255 })
		.primaryKey()
		.$defaultFn(() => crypto.randomUUID()),
	/** Record creation timestamp, defaults to current time */
	createdAt: timestamp("created_at").notNull().defaultNow(),
	/** Record update timestamp, automatically updated on changes */
	updatedAt: timestamp("updated_at")
		.notNull()
		.defaultNow()
		.$onUpdate(() => new Date()),
};

/**
 * Patient table for bi-directional sync between CC and AP platforms
 *
 * Central table that maintains patient/contact data from both CliniCore (CC) and
 * AutoPatient (AP) platforms. This table serves as the primary synchronization
 * point and enables conflict resolution by storing data from both systems.
 *
 * **Key Features:**
 * - Stores both AP contact ID and CC patient ID for cross-platform linking
 * - Maintains platform-specific update timestamps for sync coordination
 * - Preserves complete data from both platforms in JSONB columns
 * - Includes email and phone for quick lookup and matching
 * - Supports one-to-many relationship with appointments
 *
 * **Sync Logic:**
 * - Records are created when data arrives from either platform
 * - Platform-specific IDs are populated when sync occurs
 * - Update timestamps track when each platform last modified the record
 * - JSONB data preserves complete platform-specific information
 *
 * **Indexing:**
 * - Unique indexes on apId and ccId for fast platform-specific lookups
 * - Email and phone columns for duplicate detection and matching
 * - Primary key (UUID) for efficient joins with appointments
 *
 * @example
 *
```typescript
* // Find patient by CC ID
 * const patient = await db.select().from(patient).where(eq(patient.ccId, 123));
 *
 * // Find patient by AP ID
 * const patient = await db.select().from(patient).where(eq(patient.apId, "ap_123"));
 *
```
 */
export const patient = pgTable("patients", {
	...commonColumns,
	/** AutoPatient contact ID, unique identifier from AP platform */
	apId: varchar("ap_id", { length: 255 }),
	/** CliniCore patient ID, unique identifier from CC platform */
	ccId: integer("cc_id"),
	/** Patient email address for matching and communication */
	email: varchar("email", { length: 255 }),
	/** Patient phone number for matching and communication */
	phone: varchar("phone", { length: 255 }),
	/** Timestamp when patient was last updated in AP platform */
	apUpdatedAt: timestamp("ap_updated_at"),
	/** Timestamp when patient was last updated in CC platform */
	ccUpdatedAt: timestamp("cc_updated_at"),
	/** Complete AP contact data in JSONB format for preservation */
	apData: customJsonb<GetAPContactType>("ap_data"),
	/** Complete CC patient data in JSONB format for preservation */
	ccData: customJsonb<GetCCPatientType>("cc_data"),
});

export const appointment = pgTable("appointments", {
	...commonColumns,
	apId: varchar("ap_id", { length: 255 }).unique(),
	ccId: integer("cc_id").unique(),
	patientId: varchar("patient_id", { length: 255 }).references(
		() => patient.id,
	),
	apUpdatedAt: timestamp("ap_updated_at"),
	ccUpdatedAt: timestamp("cc_updated_at"),
	apData: customJsonb<GetAPAppointmentType>("ap_data"),
	ccData: customJsonb<GetCCAppointmentType>("cc_data"),
	apNoteID: text("ap_note_id"),
});

export const customFields = pgTable("custom_fields", {
	...commonColumns,
	apId: varchar("ap_id", { length: 255 }).unique(),
	ccId: integer("cc_id").unique(),
	name: varchar("name", { length: 255 }),
	label: varchar("label", { length: 255 }),
	type: varchar("type", { length: 255 }),
	apConfig: customJsonb<APGetCustomFieldType>("ap_config"),
	ccConfig: customJsonb<GetCCCustomfieldsType>("cc_config"),
	/** Mapping type: custom_to_custom, custom_to_standard, standard_to_custom */
	mappingType: varchar("mapping_type", { length: 50 }).default(
		"custom_to_custom",
	),
	/** AP standard field name if this maps to an AP standard field */
	apStandardField: varchar("ap_standard_field", { length: 255 }),
	/** CC standard field name if this maps to a CC standard field */
	ccStandardField: varchar("cc_standard_field", { length: 255 }),
});

export const errorLogs = pgTable("error_logs", {
	...commonColumns,
	requestId: varchar("request_id", { length: 255 }),
	message: text("message").notNull(),
	stack: text("stack"),
	type: varchar("type", { length: 255 }).notNull(),
	data: jsonb("data"),
});

export const webhooks = pgTable("webhooks", {
	...commonColumns,
	requestId: varchar("request_id", { length: 255 }).unique(),
	payload: jsonb("payload").notNull(),
	source: varchar("source", { length: 64, enum: ["cc", "ap"] }).notNull(),
});

/**
 * Webhook Queue Table
 *
 * Main queue table for managing webhook processing with status tracking,
 * priority ordering, and timeout detection. Designed for high-throughput
 * webhook processing with proper duplicate prevention and retry logic.
 *
 * **Key Features:**
 * - Status-based processing workflow (pending → processing → completed/failed)
 * - Priority-based ordering (fresh webhooks = 100, retries = 50)
 * - Timeout detection based on processing start time (29 seconds)
 * - Cross-platform duplicate prevention via patient/appointment relationships
 * - Comprehensive retry logic with exponential backoff
 * - Audit trail with processing timestamps
 *
 * **Status Flow:**
 * - `pending`: Webhook added to queue, waiting for processing
 * - `processing`: Webhook currently being processed (started_at set)
 * - `completed`: Webhook processed successfully
 * - `failed`: Webhook processing failed (retry_count < max_retries)
 * - `timeout`: Webhook processing exceeded 29 seconds
 * - `duplicate_skipped`: Webhook skipped due to duplicate detection
 *
 * **Indexing Strategy:**
 * - Primary index on (status, priority DESC, created_at ASC) for queue processing
 * - Index on (patient_id, entity_type) for duplicate detection
 * - Index on (appointment_id, entity_type) for appointment-based duplicates
 * - Index on started_at for timeout detection
 */
export const webhookQueue = pgTable("webhook_queue", {
	...commonColumns,
	/** Webhook processing status */
	status: varchar("status", {
		length: 50,
		enum: [
			"pending",
			"processing",
			"completed",
			"failed",
			"timeout",
			"duplicate_skipped",
		],
	})
		.notNull()
		.default("pending"),
	/** Processing priority (100 = fresh, 50 = retry, higher = more priority) */
	priority: integer("priority").notNull().default(100),
	/** Webhook source platform */
	source: varchar("source", { length: 10, enum: ["cc", "ap"] }).notNull(),
	/** Entity type being processed */
	entityType: varchar("entity_type", { length: 50 }).notNull(),
	/** Entity ID from source platform */
	entityId: varchar("entity_id", { length: 255 }).notNull(),
	/** Patient ID for cross-platform duplicate detection */
	patientId: varchar("patient_id", { length: 255 }).references(
		() => patient.id,
	),
	/** Appointment ID for appointment-based duplicate detection */
	appointmentId: varchar("appointment_id", { length: 255 }).references(
		() => appointment.id,
	),
	/** Complete webhook payload */
	payload: customJsonb<CCWebhookPayload | APWebhookPayload>(
		"payload",
	).notNull(),
	/** When processing started (for timeout detection) */
	startedAt: timestamp("started_at"),
	/** When processing completed */
	completedAt: timestamp("completed_at"),
	/** Number of retry attempts */
	retryCount: integer("retry_count").notNull().default(0),
	/** Maximum retry attempts allowed */
	maxRetries: integer("max_retries").notNull().default(3),
	/** Next retry attempt time */
	nextRetryAt: timestamp("next_retry_at"),
	/** Processing error message */
	errorMessage: text("error_message"),
	/** Processing duration in milliseconds */
	processingTimeMs: integer("processing_time_ms"),
	/** ID of original webhook if this is a duplicate */
	duplicateOf: varchar("duplicate_of", { length: 255 }),
	/** Reason for duplicate detection */
	duplicateReason: text("duplicate_reason"),
});

/**
 * Webhook Queue Logs Table
 *
 * Archive table for completed, failed, and timed-out webhooks. Maintains
 * complete processing history for audit trails, debugging, and analytics.
 * Records are moved here from webhook_queue when processing is complete.
 *
 * **Key Features:**
 * - Complete audit trail of all webhook processing
 * - Preserves all original webhook_queue data
 * - Optimized for read-only analytics and debugging
 * - Automatic cleanup policies can be applied
 * - Cross-platform processing correlation
 *
 * **Data Retention:**
 * - Completed webhooks: Moved immediately after successful processing
 * - Failed webhooks: Moved after max retries exceeded
 * - Timed-out webhooks: Moved immediately after timeout detection
 * - Duplicate webhooks: Moved immediately after duplicate detection
 */
export const webhookQueueLogs = pgTable("webhook_queue_logs", {
	...commonColumns,
	/** Final processing status */
	status: varchar("status", {
		length: 50,
		enum: ["completed", "failed", "timeout", "duplicate_skipped"],
	}).notNull(),
	/** Processing priority when queued */
	priority: integer("priority").notNull(),
	/** Webhook source platform */
	source: varchar("source", { length: 10, enum: ["cc", "ap"] }).notNull(),
	/** Entity type that was processed */
	entityType: varchar("entity_type", { length: 50 }).notNull(),
	/** Entity ID from source platform */
	entityId: varchar("entity_id", { length: 255 }).notNull(),
	/** Patient ID for correlation */
	patientId: varchar("patient_id", { length: 255 }),
	/** Appointment ID for correlation */
	appointmentId: varchar("appointment_id", { length: 255 }),
	/** Complete webhook payload */
	payload: customJsonb<CCWebhookPayload | APWebhookPayload>(
		"payload",
	).notNull(),
	/** When processing started */
	startedAt: timestamp("started_at"),
	/** When processing completed */
	completedAt: timestamp("completed_at"),
	/** Total retry attempts made */
	retryCount: integer("retry_count").notNull().default(0),
	/** Processing error message */
	errorMessage: text("error_message"),
	/** Processing duration in milliseconds */
	processingTimeMs: integer("processing_time_ms"),
	/** ID of original webhook if this was a duplicate */
	duplicateOf: varchar("duplicate_of", { length: 255 }),
	/** Reason for duplicate detection */
	duplicateReason: text("duplicate_reason"),
});

/**
 * Duplicate Prevention Locks Table
 *
 * Temporary locks for preventing duplicate webhook processing across platforms.
 * Uses patient and appointment relationships to detect when webhooks from
 * different platforms affect the same entities.
 *
 * **Key Features:**
 * - Cross-platform duplicate detection (AP ↔ CC)
 * - Patient-based and appointment-based locking
 * - Automatic cleanup of expired locks
 * - Causal relationship tracking
 * - Race condition prevention
 *
 * **Lock Types:**
 * - `patient_sync`: Prevents duplicate patient processing
 * - `appointment_sync`: Prevents duplicate appointment processing
 * - `custom_field_sync`: Prevents duplicate custom field processing
 *
 * **Cleanup Strategy:**
 * - Locks expire after 5 minutes automatically
 * - Completed webhooks remove their locks
 * - Failed webhooks keep locks until retry or expiration
 */
export const duplicatePreventionLocks = pgTable("duplicate_prevention_locks", {
	...commonColumns,
	/** Type of lock */
	lockType: varchar("lock_type", {
		length: 50,
		enum: ["patient_sync", "appointment_sync", "custom_field_sync"],
	}).notNull(),
	/** Patient ID being locked */
	patientId: varchar("patient_id", { length: 255 }).references(
		() => patient.id,
	),
	/** Appointment ID being locked */
	appointmentId: varchar("appointment_id", { length: 255 }).references(
		() => appointment.id,
	),
	/** Source platform that created the lock */
	source: varchar("source", { length: 10, enum: ["cc", "ap"] }).notNull(),
	/** Webhook ID that created the lock */
	webhookId: varchar("webhook_id", { length: 255 }).notNull(),
	/** When the lock expires */
	expiresAt: timestamp("expires_at").notNull(),
	/** Additional context for the lock */
	context: jsonb("context"),
});

/**
 * Alerts Table
 *
 * System alerts for monitoring webhook processing health, detecting issues,
 * and triggering notifications. Captures timeout events, high failure rates,
 * and other operational concerns.
 *
 * **Key Features:**
 * - Categorized alert types for different monitoring needs
 * - Severity levels for prioritization
 * - Rich context data for debugging
 * - Alert resolution tracking
 * - Integration with monitoring systems
 *
 * **Alert Types:**
 * - `webhook_timeout`: Webhook processing exceeded time limit
 * - `high_failure_rate`: Unusual number of webhook failures
 * - `queue_backlog`: Queue processing falling behind
 * - `duplicate_storm`: Excessive duplicate webhook detection
 * - `processing_error`: Critical processing errors
 *
 * **Severity Levels:**
 * - `low`: Informational alerts
 * - `medium`: Warning conditions
 * - `high`: Error conditions requiring attention
 * - `critical`: Critical issues requiring immediate action
 */
export const alerts = pgTable("alerts", {
	...commonColumns,
	/** Alert type */
	alertType: varchar("alert_type", {
		length: 50,
		enum: [
			"webhook_timeout",
			"high_failure_rate",
			"queue_backlog",
			"duplicate_storm",
			"processing_error",
		],
	}).notNull(),
	/** Alert severity */
	severity: varchar("severity", {
		length: 20,
		enum: ["low", "medium", "high", "critical"],
	}).notNull(),
	/** Alert title */
	title: varchar("title", { length: 255 }).notNull(),
	/** Alert description */
	description: text("description").notNull(),
	/** Related webhook ID */
	webhookId: varchar("webhook_id", { length: 255 }),
	/** Related patient ID */
	patientId: varchar("patient_id", { length: 255 }),
	/** Related appointment ID */
	appointmentId: varchar("appointment_id", { length: 255 }),
	/** Alert context data */
	context: jsonb("context"),
	/** Whether alert has been resolved */
	resolved: integer("resolved").notNull().default(0), // 0 = false, 1 = true (SQLite compatibility)
	/** When alert was resolved */
	resolvedAt: timestamp("resolved_at"),
	/** Who/what resolved the alert */
	resolvedBy: varchar("resolved_by", { length: 255 }),
});

export const patientAppointmentsRelation = relations(patient, ({ many }) => ({
	appointments: many(appointment),
}));

export const appointmentPatientRelation = relations(appointment, ({ one }) => ({
	patient: one(patient, {
		fields: [appointment.patientId],
		references: [patient.id],
	}),
}));

export const dbSchema = {
	patient,
	appointment,
	customFields,
	errorLogs,
	webhooks,
	webhookQueue,
	webhookQueueLogs,
	duplicatePreventionLocks,
	alerts,
};
