/**
 * Webhook Integration Layer
 *
 * Integration utilities for connecting the new webhook queue system with
 * existing webhook handlers. Provides seamless migration path and backward
 * compatibility while enabling the new queue-based processing.
 *
 * @fileoverview Webhook queue integration utilities
 * @version 1.0.0
 * @since 2025-08-06
 */

import type { APContactWebhookPayload, APWebhookPayload } from "@type";
import type { Context } from "hono";
import type { EventProcessingResult as APEventProcessingResult } from "@/processors/apWebhook";
import type {
	EventProcessingResult as CCEventProcessingResult,
	CCWebhookPayload,
} from "@/processors/ccWebhook";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { fireAndForgetWebhookIngestionFromContext } from "./fireAndForgetIntegration";
import type { WebhookProcessingResult, WebhookQueueInsert } from "./types";
import { WebhookQueueManager } from "./WebhookQueueManager";

/**
 * Integration processing result types
 */
type IntegrationResult = {
	queue?: unknown;
	direct?: unknown;
	mode: string;
	processingTime: number;
};

/**
 * Integration configuration
 */
interface IntegrationConfig {
	/** Whether to use queue processing (default: true) */
	useQueue: boolean;
	/** Whether to fall back to direct processing if queue fails */
	fallbackToDirect: boolean;
	/** Whether to process both queue and direct (for migration) */
	dualMode: boolean;
	/** Priority for queued webhooks */
	priority: number;
	/** Maximum retries for queued webhooks */
	maxRetries: number;
}

/**
 * Default integration configuration
 */
const DEFAULT_CONFIG: IntegrationConfig = {
	useQueue: true,
	fallbackToDirect: true,
	dualMode: false,
	priority: 100,
	maxRetries: 3,
};

/**
 * Webhook Queue Integration Manager
 *
 * Provides utilities for integrating the queue system with existing
 * webhook handlers. Supports gradual migration and fallback strategies.
 */
export class WebhookIntegration {
	private config: IntegrationConfig;
	private queueManager: WebhookQueueManager;

	constructor(config: Partial<IntegrationConfig> = {}) {
		this.config = { ...DEFAULT_CONFIG, ...config };
		this.queueManager = new WebhookQueueManager();
	}

	/**
	 * Process CC webhook with queue integration
	 *
	 * Integrates CC webhook processing with the queue system while
	 * maintaining backward compatibility with existing handlers.
	 *
	 * @param c - Hono context
	 * @param payload - CC webhook payload
	 * @param directProcessor - Existing direct processing function
	 * @returns Processing result
	 */
	async processCCWebhook(
		c: Context,
		payload: CCWebhookPayload,
		directProcessor?: (
			payload: CCWebhookPayload,
		) => Promise<CCEventProcessingResult>,
	): Promise<IntegrationResult | CCEventProcessingResult> {
		const startTime = Date.now();

		try {
			logInfo("Processing CC webhook with queue integration", {
				event: payload.event,
				model: payload.model,
				id: payload.id,
				useQueue: this.config.useQueue,
			});

			// Prepare queue webhook data
			const queueWebhook: WebhookQueueInsert = {
				source: "cc",
				entityType: payload.model.toLowerCase(),
				entityId: payload.id.toString(),
				payload: payload,
				priority: this.config.priority,
				maxRetries: this.config.maxRetries,
			};

			// Add patient ID if available (for duplicate detection)
			if (payload.model === "Patient" && payload.payload) {
				// Try to extract patient ID from payload or lookup existing patient
				const patientId = await this.lookupPatientId(
					"cc",
					payload.id.toString(),
				);
				if (patientId) {
					queueWebhook.patientId = patientId;
				}
			}

			let queueResult: WebhookProcessingResult | null = null;
			let directResult: CCEventProcessingResult | null = null;

			// Queue processing
			if (this.config.useQueue) {
				try {
					if (this.config.dualMode) {
						// Fire-and-forget for dual mode
						fireAndForgetWebhookIngestionFromContext(queueWebhook, {
							mode: "dual",
							originalEvent: payload.event,
						});
						// For dual mode, we don't have a real queue result
						queueResult = {
							webhookId: "fire-and-forget",
							status: "pending",
						};
					} else {
						// Direct queue processing
						queueResult = await this.queueManager.addWebhook(queueWebhook);

						// Trigger processing if not duplicate
						if (queueResult.status === "pending") {
							fireAndForgetWebhookIngestionFromContext(queueWebhook, {
								mode: "queue_only",
								originalEvent: payload.event,
							});
						}
					}

					logDebug("Queue processing initiated", {
						webhookId: queueResult?.webhookId || "fire-and-forget",
						status: queueResult?.status || "unknown",
					});
				} catch (queueError) {
					logError("Queue processing failed", queueError);

					if (!this.config.fallbackToDirect) {
						throw queueError;
					}

					logInfo("Falling back to direct processing");
				}
			}

			// Direct processing (fallback or dual mode)
			if (
				!this.config.useQueue ||
				(this.config.fallbackToDirect && !queueResult) ||
				this.config.dualMode
			) {
				if (directProcessor) {
					directResult = await directProcessor(payload);
					logDebug("Direct processing completed", {
						processingTime: Date.now() - startTime,
					});
				}
			}

			// Return appropriate result
			if (this.config.dualMode) {
				return {
					queue: queueResult,
					direct: directResult,
					mode: "dual",
					processingTime: Date.now() - startTime,
				};
			} else if (queueResult) {
				return {
					...queueResult,
					mode: "queue",
					processingTime: Date.now() - startTime,
				};
			} else {
				return {
					...directResult,
					mode: "direct",
					processingTime: Date.now() - startTime,
				};
			}
		} catch (error) {
			logError("Webhook integration processing failed", error);
			throw error;
		}
	}

	/**
	 * Process AP webhook with queue integration
	 *
	 * Integrates AP webhook processing with the queue system.
	 *
	 * @param c - Hono context
	 * @param payload - AP webhook payload
	 * @param directProcessor - Existing direct processing function
	 * @returns Processing result
	 */
	async processAPWebhook(
		c: Context,
		payload: APWebhookPayload,
		directProcessor?: (
			payload: APWebhookPayload,
		) => Promise<APEventProcessingResult>,
	): Promise<IntegrationResult | APEventProcessingResult> {
		const startTime = Date.now();

		try {
			logInfo("Processing AP webhook with queue integration", {
				contactId: payload.contact_id,
				useQueue: this.config.useQueue,
			});

			// Prepare queue webhook data
			const queueWebhook: WebhookQueueInsert = {
				source: "ap",
				entityType: "contact",
				entityId: String(payload.contact_id),
				payload: payload,
				priority: this.config.priority,
				maxRetries: this.config.maxRetries,
			};

			// Add patient ID if available (for duplicate detection)
			const patientId = await this.lookupPatientId(
				"ap",
				String(payload.contact_id),
			);
			if (patientId) {
				queueWebhook.patientId = patientId;
			}

			let queueResult: WebhookProcessingResult | null = null;
			let directResult: APEventProcessingResult | null = null;

			// Queue processing
			if (this.config.useQueue) {
				try {
					if (this.config.dualMode) {
						// Fire-and-forget for dual mode
						fireAndForgetWebhookIngestionFromContext(queueWebhook, {
							mode: "dual",
							contactId: payload.contact_id,
						});
						// For dual mode, we don't have a real queue result
						queueResult = {
							webhookId: "fire-and-forget",
							status: "pending",
						};
					} else {
						// Direct queue processing
						queueResult = await this.queueManager.addWebhook(queueWebhook);

						// Trigger processing if not duplicate
						if (queueResult.status === "pending") {
							fireAndForgetWebhookIngestionFromContext(queueWebhook, {
								mode: "queue_only",
								contactId: payload.contact_id,
							});
						}
					}

					logDebug("Queue processing initiated", {
						webhookId: queueResult?.webhookId || "fire-and-forget",
						status: queueResult?.status || "unknown",
					});
				} catch (queueError) {
					logError("Queue processing failed", queueError);

					if (!this.config.fallbackToDirect) {
						throw queueError;
					}

					logInfo("Falling back to direct processing");
				}
			}

			// Direct processing (fallback or dual mode)
			if (
				!this.config.useQueue ||
				(this.config.fallbackToDirect && !queueResult) ||
				this.config.dualMode
			) {
				if (directProcessor) {
					directResult = await directProcessor(payload);
					logDebug("Direct processing completed", {
						processingTime: Date.now() - startTime,
					});
				}
			}

			// Return appropriate result
			if (this.config.dualMode) {
				return {
					queue: queueResult,
					direct: directResult,
					mode: "dual",
					processingTime: Date.now() - startTime,
				};
			} else if (queueResult) {
				return {
					...queueResult,
					mode: "queue",
					processingTime: Date.now() - startTime,
				};
			} else {
				return {
					...directResult,
					mode: "direct",
					processingTime: Date.now() - startTime,
				};
			}
		} catch (error) {
			logError("AP webhook integration processing failed", error);
			throw error;
		}
	}

	/**
	 * Lookup patient ID for duplicate detection
	 *
	 * @param source - Platform source
	 * @param entityId - Entity ID from the source platform
	 * @returns Patient ID if found
	 */
	private async lookupPatientId(
		source: "cc" | "ap",
		entityId: string,
	): Promise<string | undefined> {
		try {
			const { getDb, dbSchema } = await import("@database");
			const { eq } = await import("drizzle-orm");
			const db = getDb();

			if (source === "cc") {
				const result = await db
					.select({ id: dbSchema.patient.id })
					.from(dbSchema.patient)
					.where(eq(dbSchema.patient.ccId, parseInt(entityId)))
					.limit(1);

				return result[0]?.id;
			} else {
				const result = await db
					.select({ id: dbSchema.patient.id })
					.from(dbSchema.patient)
					.where(eq(dbSchema.patient.apId, entityId))
					.limit(1);

				return result[0]?.id;
			}
		} catch (error) {
			logError("Failed to lookup patient ID", {
				source,
				entityId,
				error: error instanceof Error ? error.message : String(error),
			});
			return undefined;
		}
	}
}

/**
 * Create webhook integration instance with configuration
 *
 * @param config - Integration configuration
 * @returns Configured webhook integration instance
 */
export function createWebhookIntegration(
	config: Partial<IntegrationConfig> = {},
): WebhookIntegration {
	return new WebhookIntegration(config);
}

/**
 * Default webhook integration instance
 */
export const defaultWebhookIntegration = new WebhookIntegration();
