/**
 * Webhook Queue API Handlers
 *
 * HTTP handlers for webhook queue management endpoints including webhook
 * ingestion, processing triggers, and queue monitoring. Designed for
 * integration with fire-and-forget processing patterns.
 *
 * @fileoverview Queue management API handlers
 * @version 1.0.0
 * @since 2025-08-06
 */

import type { APContactWebhookPayload, APWebhookPayload } from "@type";
import type { Context } from "hono";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import type {
	PlatformSource,
	QueueProcessingOptions,
	WebhookQueueInsert,
} from "@/queue/types";
import { WebhookQueueManager } from "@/queue/WebhookQueueManager";
import { logDebug, logError, logInfo } from "@/utils/logger";

/**
 * Add webhook to queue endpoint
 *
 * Accepts webhook payloads and adds them to the processing queue with
 * duplicate detection and priority assignment. Immediately triggers
 * processing via fire-and-forget pattern.
 *
 * **Endpoint:** POST /api/queue/webhook
 *
 * **Request Body:**
 * ```json
 * {
 *   "source": "cc" | "ap",
 *   "entityType": "patient" | "appointment" | "custom_field",
 *   "entityId": "string",
 *   "patientId": "string", // optional
 *   "appointmentId": "string", // optional
 *   "payload": { ... }, // webhook payload
 *   "priority": 100, // optional, default 100
 *   "maxRetries": 3 // optional, default 3
 * }
 * ```
 *
 * **Response:**
 * ```json
 * {
 *   "success": true,
 *   "webhookId": "uuid",
 *   "status": "pending" | "duplicate_skipped",
 *   "duplicateOf": "uuid", // if duplicate
 *   "duplicateReason": "string" // if duplicate
 * }
 * ```
 *
 * @param c - Hono context object
 * @returns HTTP Response with webhook processing result
 */
export async function addWebhookToQueue(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received webhook queue request");

		// Parse and validate request body
		const body = await c.req.json();
		const webhook = validateWebhookRequest(body);

		if (!webhook) {
			return c.json(
				{
					success: false,
					error: "Invalid webhook request",
					message: "Missing required fields or invalid data format",
					timestamp,
				},
				400,
			);
		}

		// Add webhook to queue
		const queueManager = new WebhookQueueManager();
		const result = await queueManager.addWebhook(webhook);

		logInfo("Webhook added to queue", {
			webhookId: result.webhookId,
			status: result.status,
			source: webhook.source,
			entityType: webhook.entityType,
		});

		// Trigger processing immediately (fire-and-forget)
		if (result.status === "pending") {
			triggerQueueProcessing();
		}

		return c.json({
			success: true,
			webhookId: result.webhookId,
			status: result.status,
			duplicateOf: result.duplicateOf,
			duplicateReason: result.duplicateReason,
			timestamp,
		});
	} catch (error) {
		logError("Failed to add webhook to queue", error);

		return c.json(
			{
				success: false,
				error: "Internal server error",
				message: "Failed to process webhook request",
				timestamp,
			},
			500,
		);
	}
}

/**
 * Process queue endpoint
 *
 * Triggers processing of pending webhooks in the queue. Designed to be
 * called via fire-and-forget pattern for immediate processing or as a
 * manual trigger for queue processing.
 *
 * **Endpoint:** POST /api/queue/process
 *
 * **Request Body (optional):**
 * ```json
 * {
 *   "triggerId": "string", // optional, for correlation
 *   "batchSize": 3, // optional, default 3, max 3
 *   "maxProcessingTimeMs": 29000 // optional, default 29000
 * }
 * ```
 *
 * **Response:**
 * ```json
 * {
 *   "success": true,
 *   "processedCount": 2,
 *   "batchResults": [
 *     {
 *       "webhookId": "uuid",
 *       "status": "completed" | "failed" | "duplicate_skipped" | "timeout",
 *       "processingTimeMs": 150,
 *       "errorMessage": "string" // if failed
 *     }
 *   ],
 *   "retryCount": 1,
 *   "alertsCreated": 0,
 *   "timeoutsDetected": 0
 * }
 * ```
 *
 * @param c - Hono context object
 * @returns HTTP Response with batch processing results
 */
export async function processQueue(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received queue processing request");

		// Parse optional request body
		let options: QueueProcessingOptions = {};
		try {
			const body = await c.req.json();
			options = {
				batchSize: Math.min(body.batchSize || 3, 3), // Max 3 for safety
				maxProcessingTimeMs: body.maxProcessingTimeMs || 29000,
			};
		} catch {
			// No body or invalid JSON, use defaults
		}

		const queueManager = new WebhookQueueManager();

		// Detect and handle timeouts first
		const timeoutResult = await queueManager.detectTimeouts();
		logDebug("Timeout detection completed", {
			timedOutCount: timeoutResult.timedOutWebhooks.length,
			alertsCreated: timeoutResult.alertsCreated,
		});

		// Process batch
		const batchResult = await queueManager.processBatch(options);

		// Cleanup expired locks
		const cleanedLocks = await queueManager.cleanupExpiredLocks();

		logInfo("Queue processing completed", {
			processedCount: batchResult.processedCount,
			retryCount: batchResult.retryCount,
			alertsCreated: batchResult.alertsCreated,
			timeoutsDetected: timeoutResult.timedOutWebhooks.length,
			cleanedLocks,
		});

		return c.json({
			success: batchResult.success,
			processedCount: batchResult.processedCount,
			batchResults: batchResult.batchResults,
			retryCount: batchResult.retryCount,
			alertsCreated: batchResult.alertsCreated,
			timeoutsDetected: timeoutResult.timedOutWebhooks.length,
			cleanedLocks,
			timestamp,
		});
	} catch (error) {
		logError("Failed to process queue", error);

		return c.json(
			{
				success: false,
				error: "Internal server error",
				message: "Failed to process queue",
				timestamp,
			},
			500,
		);
	}
}

/**
 * Get queue statistics endpoint
 *
 * Returns current queue statistics for monitoring and debugging.
 *
 * **Endpoint:** GET /api/queue/stats
 *
 * @param c - Hono context object
 * @returns HTTP Response with queue statistics
 */
export async function getQueueStatistics(c: Context): Promise<Response> {
	try {
		const queueManager = new WebhookQueueManager();
		const stats = await queueManager.getQueueStatistics();

		return c.json({
			success: true,
			statistics: stats,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		logError("Failed to get queue statistics", error);

		return c.json(
			{
				success: false,
				error: "Internal server error",
				message: "Failed to retrieve queue statistics",
			},
			500,
		);
	}
}

/**
 * Validate webhook request data
 *
 * @param body - Request body to validate
 * @returns Validated webhook data or null if invalid
 */
function validateWebhookRequest(body: unknown): WebhookQueueInsert | null {
	if (!body || typeof body !== "object") {
		return null;
	}

	const data = body as Record<string, unknown>;

	// Required fields
	if (
		typeof data.source !== "string" ||
		!["cc", "ap"].includes(data.source) ||
		typeof data.entityType !== "string" ||
		typeof data.entityId !== "string" ||
		!data.payload ||
		typeof data.payload !== "object"
	) {
		return null;
	}

	return {
		source: data.source as PlatformSource,
		entityType: data.entityType,
		entityId: data.entityId,
		patientId: typeof data.patientId === "string" ? data.patientId : undefined,
		appointmentId:
			typeof data.appointmentId === "string" ? data.appointmentId : undefined,
		payload: data.payload as CCWebhookPayload | APWebhookPayload, // Type will be validated by the queue manager
		priority: typeof data.priority === "number" ? data.priority : undefined,
		maxRetries:
			typeof data.maxRetries === "number" ? data.maxRetries : undefined,
	};
}

/**
 * Trigger queue processing via fire-and-forget
 *
 * Uses the improved fire-and-forget queue processing function that automatically
 * extracts the base URL from the current Hono context.
 */
function triggerQueueProcessing(): void {
	try {
		// Import fire-and-forget utility dynamically to avoid circular imports
		import("@/queue/fireAndForgetIntegration")
			.then(({ fireAndForgetQueueProcessing }) => {
				fireAndForgetQueueProcessing(
					{},
					{
						trigger: "webhook_added",
						timestamp: new Date().toISOString(),
					},
				);
			})
			.catch((error) => {
				logError("Failed to trigger queue processing", error);
			});
	} catch (error) {
		logError("Failed to setup queue processing trigger", error);
	}
}
