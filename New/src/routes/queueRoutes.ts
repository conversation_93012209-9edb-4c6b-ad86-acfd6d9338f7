/**
 * Webhook Queue Routes
 *
 * HTTP route definitions for the webhook queue system. Provides endpoints
 * for webhook ingestion, queue processing, and monitoring.
 *
 * @fileoverview Queue system HTTP routes
 * @version 1.0.0
 * @since 2025-08-06
 */

import { dbSchema, getDb } from "@database";
import type { APContactWebhookPayload, APWebhookPayload } from "@type";
import { eq } from "drizzle-orm";
import { Hono } from "hono";
import {
	addWebhookToQueue,
	getQueueStatistics,
	processQueue,
} from "@/handlers/queueHandler";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import { logDebug, logError } from "@/utils/logger";

/**
 * Create queue routes
 *
 * Sets up all HTTP routes for the webhook queue system including
 * webhook ingestion, processing triggers, and monitoring endpoints.
 *
 * **Available Routes:**
 * - POST /webhook - Add webhook to queue
 * - POST /process - Trigger queue processing
 * - GET /stats - Get queue statistics
 *
 * @returns Hono app with queue routes configured
 */
export function createQueueRoutes(): Hono {
	const app = new Hono();

	/**
	 * Add webhook to queue
	 * POST /api/queue/webhook
	 */
	app.post("/webhook", addWebhookToQueue);

	/**
	 * Trigger queue processing
	 * POST /api/queue/process
	 */
	app.post("/process", processQueue);

	/**
	 * Get queue statistics
	 * GET /api/queue/stats
	 */
	app.get("/stats", getQueueStatistics);

	/**
	 * Health check endpoint
	 * GET /api/queue/health
	 */
	app.get("/health", async (c) => {
		return c.json({
			status: "healthy",
			service: "webhook-queue",
			timestamp: new Date().toISOString(),
		});
	});

	return app;
}

/**
 * Look up patient ID from CliniCore webhook payload
 *
 * Searches for an existing patient record using the CC patient ID from the webhook.
 * This function is used to enable cross-platform duplicate detection in the queue system.
 *
 * @param ccPatientId - CliniCore patient ID from webhook payload
 * @returns Patient ID if found, undefined otherwise
 */
async function lookupPatientIdFromCCWebhook(
	ccPatientId: number,
): Promise<string | undefined> {
	try {
		const db = getDb();
		const result = await db
			.select({ id: dbSchema.patient.id })
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.ccId, ccPatientId))
			.limit(1);

		const patientId = result[0]?.id;
		if (patientId) {
			logDebug("Found patient ID for CC webhook", {
				ccPatientId,
				patientId,
			});
		} else {
			logDebug("No patient found for CC webhook", { ccPatientId });
		}

		return patientId;
	} catch (error) {
		logError("Failed to lookup patient ID from CC webhook", {
			ccPatientId,
			error: error instanceof Error ? error.message : String(error),
		});
		return undefined;
	}
}

/**
 * Look up patient ID from AutoPatient webhook payload
 *
 * Searches for an existing patient record using the AP contact ID from the webhook.
 * This function is used to enable cross-platform duplicate detection in the queue system.
 *
 * @param apContactId - AutoPatient contact ID from webhook payload
 * @returns Patient ID if found, undefined otherwise
 */
async function lookupPatientIdFromAPWebhook(
	apContactId: string,
): Promise<string | undefined> {
	try {
		const db = getDb();
		const result = await db
			.select({ id: dbSchema.patient.id })
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.apId, apContactId))
			.limit(1);

		const patientId = result[0]?.id;
		if (patientId) {
			logDebug("Found patient ID for AP webhook", {
				apContactId,
				patientId,
			});
		} else {
			logDebug("No patient found for AP webhook", { apContactId });
		}

		return patientId;
	} catch (error) {
		logError("Failed to lookup patient ID from AP webhook", {
			apContactId,
			error: error instanceof Error ? error.message : String(error),
		});
		return undefined;
	}
}

/**
 * Example integration with existing webhook handlers
 *
 * Shows how to integrate the queue system with existing webhook handlers
 * for seamless processing without breaking existing functionality.
 */
export const queueIntegrationExample = {
	/**
	 * Example: Integrate with CC webhook handler
	 */
	async integrateWithCCWebhook(payload: CCWebhookPayload) {
		// This would be added to the existing CC webhook handler
		const { fireAndForgetWebhookIngestionFromContext } = await import(
			"@/queue/fireAndForgetIntegration"
		);

		// Look up patient ID for cross-platform duplicate detection
		const patientId = await lookupPatientIdFromCCWebhook(payload.id);

		// Add to queue for processing
		fireAndForgetWebhookIngestionFromContext({
			source: "cc",
			entityType: "patient",
			entityId: payload.id.toString(),
			patientId: patientId, // Now properly looked up from patient table
			payload: payload,
		});
	},

	/**
	 * Example: Integrate with AP webhook handler
	 */
	async integrateWithAPWebhook(payload: APWebhookPayload) {
		// This would be added to the existing AP webhook handler
		const { fireAndForgetWebhookIngestionFromContext } = await import(
			"@/queue/fireAndForgetIntegration"
		);

		// Look up patient ID for cross-platform duplicate detection
		const patientId = await lookupPatientIdFromAPWebhook(
			String(payload.contact_id),
		);

		// Add to queue for processing
		fireAndForgetWebhookIngestionFromContext({
			source: "ap",
			entityType: "contact",
			entityId: String(payload.contact_id),
			patientId: patientId, // Now properly looked up from patient table
			payload: payload,
		});
	},
};
